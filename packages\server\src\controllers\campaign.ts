import AthleteModel from "../models/athlete";
import BrandModel from "../models/brand";
import {
  CampaignApplicationModel,
  CampaignModel,
  serializeCampaign,
  serializeCampaignApplication,
} from "../models/campaign";
import DeliverableModel from "../models/deliverable";
import { UserType } from "../models/user";
import { ApplicationStatus, CampaignStatus, CampaignVisibility } from "../types/campaign";
import { MessageType, ChatType } from "../types/chat";
import { ExtendedTRPCError } from "../utils/trpc";
import { createChat, sendMessage } from "./chat";

export const createCampaign = async (
  userId: string,
  userType: UserType,
  input: any,
) => {
  if (userType !== "brand") {
    throw new ExtendedTRPCError(
      "FORBIDDEN",
      "Only brand users can create campaigns",
    );
  }

  const brand = await BrandModel.findOne({ userId });
  if (!brand) {
    throw new ExtendedTRPCError("NOT_FOUND", "Brand profile not found");
  }

  // Use provided status or default to DRAFT
  const status = input.status || CampaignStatus.ACTIVE;

  const campaign = new CampaignModel({
    ...input,
    brandId: brand._id,
    status,
  });

  await campaign.save();

  // Create deliverables for this campaign
  if (Array.isArray(input.deliverables)) {
    await Promise.all(
      input.deliverables.map((d: any) =>
        DeliverableModel.create({
          ...d,
          campaignId: campaign._id,
        }),
      ),
    );
  }

  return campaign;
};

export const updateCampaign = async (
  userId: string,
  campaignId: string,
  input: any,
) => {
  const brand = await BrandModel.findOne({ userId });
  if (!brand) {
    throw new ExtendedTRPCError("NOT_FOUND", "Brand profile not found");
  }

  const campaign = await CampaignModel.findOne({
    _id: campaignId,
    brandId: brand._id,
  });
  if (!campaign) {
    throw new ExtendedTRPCError("NOT_FOUND", "Campaign not found");
  }

  // Don't allow updates to completed or cancelled campaigns
  if (
    campaign.status === CampaignStatus.COMPLETED ||
    campaign.status === CampaignStatus.CANCELLED
  ) {
    throw new ExtendedTRPCError(
      "BAD_REQUEST",
      "Cannot update completed or cancelled campaigns",
    );
  }

  // Update campaign fields, preserving existing status if not explicitly provided
  const { status: newStatus, ...otherFields } = input;

  // Update all fields except status
  Object.assign(campaign, otherFields);

  // Only update status if it's explicitly provided and not undefined
  if (newStatus !== undefined) {
    campaign.status = newStatus;
  }

  await campaign.save();

  // Remove old deliverables
  await DeliverableModel.deleteMany({ campaignId: campaign._id });

  // Create new deliverables
  if (Array.isArray(input.deliverables)) {
    await Promise.all(
      input.deliverables.map((d: any) =>
        DeliverableModel.create({
          ...d,
          campaignId: campaign._id,
        }),
      ),
    );
  }

  return campaign;
};

export const updateCampaignStatus = async (
  userId: string,
  campaignId: string,
  status: CampaignStatus,
) => {
  const brand = await BrandModel.findOne({ userId });
  if (!brand) {
    throw new ExtendedTRPCError("NOT_FOUND", "Brand profile not found");
  }

  const campaign = await CampaignModel.findOne({
    _id: campaignId,
    brandId: brand._id,
  });
  if (!campaign) {
    throw new ExtendedTRPCError("NOT_FOUND", "Campaign not found");
  }

  campaign.status = status;
  await campaign.save();
  return campaign;
};

export const getBrandCampaigns = async (userId: string) => {
  const brand = await BrandModel.findOne({ userId });
  if (!brand) {
    throw new ExtendedTRPCError("NOT_FOUND", "Brand profile not found");
  }

  const campaigns = await CampaignModel.find({ brandId: brand._id }).sort({
    createdAt: -1,
  });
  return Promise.all(campaigns.map((c) => serializeCampaign(c)));
};

export const getActiveCampaigns = async (
  page: number = 1,
  limit: number = 10,
  filters?: any,
) => {
  const query: any = {
    status: CampaignStatus.ACTIVE,
    endDate: { $gte: new Date() },
    visibility: CampaignVisibility.PUBLIC, // Only show public campaigns to athletes
  };

  if (filters) {
    if (filters.categories?.length) {
      query.categories = { $in: filters.categories };
    }
    if (filters.location) {
      query.location = filters.location;
    }
    if (filters.minPrice !== undefined) {
      query.price = { ...query.price, $gte: filters.minPrice };
    }
    if (filters.maxPrice !== undefined) {
      query.price = { ...query.price, $lte: filters.maxPrice };
    }
    if (filters.search) {
      query.$or = [
        { name: { $regex: filters.search, $options: "i" } },
        { description: { $regex: filters.search, $options: "i" } },
      ];
    }
    if (filters.daysToComplete) {
      const now = new Date();
      const daysInMilliseconds = (days: number) => days * 24 * 60 * 60 * 1000;

      // Calculate days until end date
      query.endDate = {
        $gt: now, // Must be in the future
      };

      if (filters.daysToComplete.min !== undefined) {
        query.endDate.$gt = new Date(
          now.getTime() + daysInMilliseconds(filters.daysToComplete.min - 1),
        );
      }
      if (filters.daysToComplete.max !== undefined) {
        query.endDate.$lte = new Date(
          now.getTime() + daysInMilliseconds(filters.daysToComplete.max),
        );
      }
    }

    if (filters.deliverables) {
      const conditions = [];

      if (filters.deliverables.min !== undefined) {
        conditions.push({
          $gte: [{ $size: "$deliverables" }, filters.deliverables.min],
        });
      }
      if (filters.deliverables.max !== undefined) {
        conditions.push({
          $lte: [{ $size: "$deliverables" }, filters.deliverables.max],
        });
      }

      if (conditions.length > 0) {
        query.$expr =
          conditions.length === 1 ? conditions[0] : { $and: conditions };
      }
    }
  }

  const total = await CampaignModel.countDocuments(query);
  const campaigns = await CampaignModel.find(query)
    .sort({ createdAt: -1 })
    .skip((page - 1) * limit)
    .limit(limit);

  return {
    campaigns: await Promise.all(campaigns.map((c) => serializeCampaign(c))),
    total,
    page,
    totalPages: Math.ceil(total / limit),
  };
};

export const applyCampaign = async (
  userId: string,
  userType: UserType,
  input: any,
) => {
  if (userType !== "athlete") {
    throw new ExtendedTRPCError(
      "FORBIDDEN",
      "Only athletes can apply to campaigns",
    );
  }

  const campaign = await CampaignModel.findById(input.campaignId);
  if (!campaign) {
    throw new ExtendedTRPCError("NOT_FOUND", "Campaign not found");
  }

  if (campaign.status !== CampaignStatus.ACTIVE) {
    throw new ExtendedTRPCError("BAD_REQUEST", "Campaign is not active");
  }

  // Check if campaign is still open
  if (campaign.endDate < new Date()) {
    throw new ExtendedTRPCError(
      "BAD_REQUEST",
      "Campaign is not currently open",
    );
  }

  // Get the athlete's profile ID
  const athlete = await AthleteModel.findOne({ userId });
  if (!athlete) {
    throw new ExtendedTRPCError("NOT_FOUND", "Athlete profile not found");
  }

  // Create application
  const application = new CampaignApplicationModel({
    campaignId: campaign._id,
    athleteId: athlete._id, // Use the athlete's profile ID, not the user ID
    status: ApplicationStatus.PENDING,
    message: input.message,
    compensation: input.compensation,
    deliverables: input.deliverables,
  });

  await application.save();
  return application;
};

export const getAthleteApplications = async (userId: string) => {
  // First get the athlete's profile
  const athlete = await AthleteModel.findOne({ userId });
  if (!athlete) {
    throw new ExtendedTRPCError("NOT_FOUND", "Athlete profile not found");
  }

  console.log("Found athlete profile:", athlete._id);

  const applications = await CampaignApplicationModel.find({
    athleteId: athlete._id,
  })
    .populate("campaignId")
    .sort({ createdAt: -1 });

  console.log("Found applications:", applications.length);

  const serialized = applications.map(serializeCampaignApplication);
  console.log("Serialized applications:", serialized.length);

  return serialized;
};

export const getCampaignApplications = async (
  userId: string,
  campaignId: string,
) => {
  const brand = await BrandModel.findOne({ userId });
  if (!brand) {
    throw new ExtendedTRPCError("NOT_FOUND", "Brand profile not found");
  }

  const campaign = await CampaignModel.findOne({
    _id: campaignId,
    brandId: brand._id,
  });
  if (!campaign) {
    throw new ExtendedTRPCError("NOT_FOUND", "Campaign not found");
  }

  const applications = await CampaignApplicationModel.find({ campaignId })
    .populate({
      path: "athleteId",
      model: "Athletes",
      select: "userId profilePicture",
      populate: {
        path: "userId",
        model: "Users",
        select: "name",
      },
    })
    .sort({ createdAt: -1 });

  const serialized = applications.map(serializeCampaignApplication);

  return serialized;
};

export const updateApplicationStatus = async (userId: string, input: any) => {
  console.log("updateApplicationStatus", userId, input);
  const brand = await BrandModel.findOne({ userId });
  if (!brand) {
    throw new ExtendedTRPCError("NOT_FOUND", "Brand profile not found");
  }

  const application = await CampaignApplicationModel.findById(
    input.applicationId,
  ).populate({
    path: "campaignId",
    model: "Campaigns",
  });

  if (!application) {
    throw new ExtendedTRPCError("NOT_FOUND", "Application not found");
  }

  const campaign = await CampaignModel.findById(application.campaignId);
  if (!campaign) {
    throw new ExtendedTRPCError("NOT_FOUND", "Campaign not found");
  }

  // Verify the campaign belongs to this brand
  if (campaign.brandId.toString() !== brand._id.toString()) {
    throw new ExtendedTRPCError(
      "FORBIDDEN",
      "You don't have permission to update this application",
    );
  }

  // Get the athlete to find their user ID for messaging
  const athlete = await AthleteModel.findById(application.athleteId);
  if (!athlete) {
    throw new ExtendedTRPCError("NOT_FOUND", "Athlete not found");
  }

  application.status = input.status;
  await application.save();

  // If the application is accepted, add the athlete to the campaign's athletes array
  if (input.status === ApplicationStatus.ACCEPTED) {
    await CampaignModel.updateOne(
      { _id: campaign._id },
      { $addToSet: { athletes: application.athleteId } },
    );

    // Note: Contract generation is now handled manually by brands through the UI
    // This allows brands to review applications first and generate contracts when ready
    // The contract generation can be triggered from the campaign applications page
  }

  // Send notification message to the athlete
  try {
    // Create or get the chat between brand and athlete
    const chat = await createChat(
      userId, // brand user ID
      [athlete.userId.toString()], // athlete user ID
      ChatType.DIRECT,
      campaign._id.toString()
    );

    // Determine message content and type based on status
    const isAccepted = input.status === ApplicationStatus.ACCEPTED;
    const messageContent = isAccepted
      ? `Great news! Your application for the campaign "${campaign.name}" has been accepted. Welcome to the campaign!`
      : `Thank you for your interest in the campaign "${campaign.name}". Unfortunately, your application was not selected this time.`;

    const messageType = isAccepted
      ? MessageType.APPLICATION_ACCEPTED
      : MessageType.APPLICATION_REJECTED;

    // Send the notification message
    await sendMessage(
      chat.id,
      userId, // brand user ID as sender
      messageContent,
      messageType,
      campaign._id.toString()
    );
  } catch (error) {
    // Log the error but don't fail the application status update
    console.error("Failed to send application status notification:", error);
  }

  return application;
};

export const deleteCampaign = async (userId: string, campaignId: string) => {
  const brand = await BrandModel.findOne({ userId });
  if (!brand) {
    throw new ExtendedTRPCError("NOT_FOUND", "Brand profile not found");
  }
  const campaign = await CampaignModel.findOne({
    _id: campaignId,
    brandId: brand._id,
  });
  if (!campaign) {
    throw new ExtendedTRPCError("NOT_FOUND", "Campaign not found");
  }
  // Delete all applications for this campaign
  await CampaignApplicationModel.deleteMany({ campaignId: campaign._id });
  // Delete the campaign
  await CampaignModel.deleteOne({ _id: campaign._id });
  return { success: true };
};

export const getCampaignById = async (userId: string, campaignId: string) => {
  const brand = await BrandModel.findOne({ userId });
  if (!brand) {
    throw new ExtendedTRPCError("NOT_FOUND", "Brand profile not found");
  }
  const campaign = await CampaignModel.findOne({
    _id: campaignId,
    brandId: brand._id,
  });
  if (!campaign) {
    throw new ExtendedTRPCError("NOT_FOUND", "Campaign not found");
  }
  return await serializeCampaign(campaign);
};

export const getCampaignByCampaignId = async (campaignId: string) => {
  const campaign = await CampaignModel.findById(campaignId)
    .populate({
      path: "athletes",
      model: "Athletes",
      select: "name profilePicture",
    })
    .lean();
  if (!campaign) {
    throw new ExtendedTRPCError("NOT_FOUND", "Campaign not found");
  }
  return await serializeCampaign(campaign);
};

export const getCampaignsForAthlete = async (userId: string) => {
  // Find the athlete profile by userId
  const athlete = await AthleteModel.findOne({ userId });
  if (!athlete) {
    throw new ExtendedTRPCError("NOT_FOUND", "Athlete profile not found");
  }
  // Find all campaigns where this athlete is in the athletes array
  const campaigns = await CampaignModel.find({ athletes: athlete._id }).sort({
    createdAt: -1,
  });
  return Promise.all(campaigns.map((c) => serializeCampaign(c)));
};

export const getCampaignsByBrandId = async (brandId: string) => {
  const campaigns = await CampaignModel.find({ brandId }).sort({
    createdAt: -1,
  });
  return Promise.all(campaigns.map((c) => serializeCampaign(c)));
};

export const inviteAthletesToCampaign = async (
  userId: string,
  userType: UserType,
  input: {
    campaignId: string;
    athleteIds: string[];
    deliverablePricing?: Record<string, number>; // athlete-specific pricing per deliverable
  },
) => {
  if (userType !== "brand") {
    throw new ExtendedTRPCError(
      "FORBIDDEN",
      "Only brand users can invite athletes to campaigns",
    );
  }

  const brand = await BrandModel.findOne({ userId });
  if (!brand) {
    throw new ExtendedTRPCError("NOT_FOUND", "Brand profile not found");
  }

  const campaign = await CampaignModel.findOne({
    _id: input.campaignId,
    brandId: brand._id,
  });
  if (!campaign) {
    throw new ExtendedTRPCError("NOT_FOUND", "Campaign not found");
  }

  if (campaign.status !== CampaignStatus.ACTIVE) {
    throw new ExtendedTRPCError("BAD_REQUEST", "Campaign is not active");
  }

  // Get campaign deliverables to create deliverable data with athlete-specific pricing
  const campaignDeliverables = await DeliverableModel.find({
    campaignId: campaign._id,
  });

  // Create deliverable data with athlete-specific pricing if provided
  const deliverableData = input.deliverablePricing
    ? campaignDeliverables.map((deliverable) => ({
        name: deliverable.name,
        description: deliverable.description,
        daysToComplete: deliverable.daysToComplete,
        minimumPayment: input.deliverablePricing![deliverable.id] || deliverable.minimumPayment,
        type: deliverable.type,
      }))
    : [];

  // Create applications for each athlete
  const applications = await Promise.all(
    input.athleteIds.map(async (athleteId) => {
      // Check if application already exists
      const existingApplication = await CampaignApplicationModel.findOne({
        campaignId: campaign._id,
        athleteId,
      });

      if (existingApplication) {
        return existingApplication;
      }

      // Create new application with athlete-specific deliverable pricing
      const application = new CampaignApplicationModel({
        campaignId: campaign._id,
        athleteId,
        status: ApplicationStatus.PENDING,
        deliverables: deliverableData, // Include athlete-specific pricing
      });

      await application.save();
      return application;
    }),
  );

  return applications;
};


export const respondToCampaignInvite = async (
  userId: string,
  userType: UserType,
  input: { campaignId: string; accept: boolean },
) => {
  if (userType !== "athlete") {
    throw new ExtendedTRPCError(
      "FORBIDDEN",
      "Only athletes can respond to campaign invites",
    );
  }

  const athlete = await AthleteModel.findOne({ userId });
  if (!athlete) {
    throw new ExtendedTRPCError("NOT_FOUND", "Athlete profile not found");
  }

  const campaign = await CampaignModel.findById(input.campaignId);
  if (!campaign) {
    throw new ExtendedTRPCError("NOT_FOUND", "Campaign not found");
  }

  if (campaign.status !== CampaignStatus.ACTIVE) {
    throw new ExtendedTRPCError("BAD_REQUEST", "Campaign is not active");
  }

  // Check if campaign is still open
  if (campaign.endDate < new Date()) {
    throw new ExtendedTRPCError(
      "BAD_REQUEST",
      "Campaign is no longer open",
    );
  }

  if (input.accept) {
    // Add athlete to campaign if accepting
    await CampaignModel.updateOne(
      { _id: campaign._id },
      { $addToSet: { athletes: athlete._id } },
    );

    // Update existing application status to accepted
    const existingApplication = await CampaignApplicationModel.findOne({
      campaignId: campaign._id,
      athleteId: athlete._id,
    });

    if (existingApplication) {
      existingApplication.status = ApplicationStatus.ACCEPTED;
      await existingApplication.save();
    } else {
      // This shouldn't happen since invitations create applications, but handle it gracefully
      const application = new CampaignApplicationModel({
        campaignId: campaign._id,
        athleteId: athlete._id,
        status: ApplicationStatus.ACCEPTED,
        message: "Accepted campaign invitation",
        deliverables: [], // Empty since no pricing data available
      });
      await application.save();
    }
  } else {
    // Remove athlete from campaign if rejecting (in case they were already added)
    await CampaignModel.updateOne(
      { _id: campaign._id },
      { $pull: { athletes: athlete._id } },
    );

    // Update existing application status to rejected
    const existingApplication = await CampaignApplicationModel.findOne({
      campaignId: campaign._id,
      athleteId: athlete._id,
    });

    if (existingApplication) {
      existingApplication.status = ApplicationStatus.REJECTED;
      await existingApplication.save();
    } else {
      // This shouldn't happen since invitations create applications, but handle it gracefully
      const application = new CampaignApplicationModel({
        campaignId: campaign._id,
        athleteId: athlete._id,
        status: ApplicationStatus.REJECTED,
        message: "Declined campaign invitation",
        deliverables: [], // Empty since no pricing data available
      });
      await application.save();
    }
  }

  return { success: true, accepted: input.accept };
};

/**
 * Check if athletes have existing relationships with a campaign to prevent duplicate invitations.
 * Returns information about each athlete's current relationship status with the campaign.
 *
 * @param userId - Brand user ID making the request
 * @param userType - Must be "brand"
 * @param input - Campaign ID and array of athlete IDs to check
 * @returns Array of relationship objects with status information for each athlete
 */
export const checkAthleteRelationshipWithCampaign = async (
  userId: string,
  userType: UserType,
  input: { campaignId: string; athleteIds: string[] },
) => {
  if (userType !== "brand") {
    throw new ExtendedTRPCError(
      "FORBIDDEN",
      "Only brand users can check athlete relationships",
    );
  }

  const brand = await BrandModel.findOne({ userId });
  if (!brand) {
    throw new ExtendedTRPCError("NOT_FOUND", "Brand profile not found");
  }

  const campaign = await CampaignModel.findOne({
    _id: input.campaignId,
    brandId: brand._id,
  });
  if (!campaign) {
    throw new ExtendedTRPCError("NOT_FOUND", "Campaign not found");
  }

  const relationships = await Promise.all(
    input.athleteIds.map(async (athleteId) => {
      // Check if athlete is already a confirmed participant
      const isParticipant = campaign.athletes?.includes(athleteId as any);

      // Check for existing applications
      const existingApplication = await CampaignApplicationModel.findOne({
        campaignId: campaign._id,
        athleteId,
      });

      let relationshipType: string | null = null;
      let relationshipStatus: string | null = null;

      if (isParticipant) {
        relationshipType = "participant";
        relationshipStatus = "confirmed";
      } else if (existingApplication) {
        relationshipType = "application";
        relationshipStatus = existingApplication.status;
      }

      return {
        athleteId,
        hasRelationship: !!(isParticipant || existingApplication),
        relationshipType,
        relationshipStatus,
      };
    }),
  );

  return relationships;
};
