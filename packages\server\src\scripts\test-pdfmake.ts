import { pdfMakeGenerator, ContractPdfData } from '../services/pdfMakeGenerator';
import { ContractStatus, ContractType, PaymentStatus } from '../types/contract';
import { CampaignStatus, CampaignVisibility } from '../types/campaign';
import { Types } from 'mongoose';
import * as fs from 'fs';
import * as path from 'path';

// Mock data for testing
const mockContractData: ContractPdfData = {
  contract: {
    id: 'test-contract-id',
    contractNumber: 'TEST-001',
    version: 1,
    status: ContractStatus.DRAFT,
    type: ContractType.CAMPAIGN_AGREEMENT,
    title: 'Summer Product Launch Campaign Agreement',
    terms: {
      totalCompensation: 1500,
      deliverables: [
        {
          deliverableId: 'del-1',
          name: 'Instagram Post',
          description: 'Create an engaging Instagram post featuring the product',
          compensation: 750,
          dueDate: '2025-08-01T00:00:00.000Z',
          requirements: ['High-quality photo', 'Brand mention', 'Hashtags'],
          type: 'content_creation'
        },
        {
          deliverableId: 'del-2',
          name: 'Story Series',
          description: 'Create a 3-part Instagram story series',
          compensation: 750,
          dueDate: '2025-08-05T00:00:00.000Z',
          requirements: ['3 story posts', 'Brand tag', 'Product showcase'],
          type: 'content_creation'
        }
      ],
      paymentSchedule: [],
      campaignDuration: {
        startDate: '2025-07-01T00:00:00.000Z',
        endDate: '2025-08-31T00:00:00.000Z'
      }
    },
    brandId: 'brand-123',
    athleteId: 'athlete-456',
    campaignId: 'campaign-789',
    applicationId: 'application-123',
    participants: [],
    createdAt: '2025-07-01T00:00:00.000Z',
    updatedAt: '2025-07-01T00:00:00.000Z',
    brandSignedAt: undefined,
    athleteSignedAt: undefined,
    pdfUrl: undefined,
    paymentIntentId: undefined,
    paymentStatus: PaymentStatus.PENDING,
    expiresAt: '2025-12-31T00:00:00.000Z'
  },
  campaign: {
    id: 'campaign-789',
    name: 'Summer Product Launch',
    description: 'Promote our new summer collection',
    price: 1650, // includes processing fee + 10% AIMS fee
    status: CampaignStatus.ACTIVE,
    brandId: 'brand-123',
    createdAt: '2025-07-01T00:00:00.000Z',
    updatedAt: '2025-07-01T00:00:00.000Z',
    deliverables: [],
    startDate: '2025-07-01T00:00:00.000Z',
    endDate: '2025-08-31T00:00:00.000Z',
    visibility: CampaignVisibility.PUBLIC,
    interests: ['fitness', 'lifestyle']
  },
  brand: {
    _id: 'brand-123',
    companyName: 'Test Brand Co.',
    industry: 'Fashion',
    website: 'https://testbrand.com',
    location: 'New York, NY',
    description: 'A test brand for PDF generation',
    logo: {
      url: null,
      key: null,
      uploadedAt: null
    },
    subscriptionActive: true,
    stripeSubscriptionId: null,
    userId: new Types.ObjectId('507f1f77bcf86cd799439011')
  },
  athlete: {
    _id: 'athlete-456',
    name: 'John Doe',
    university: 'State University',
    sport: 'Basketball',
    yearInSchool: 'Senior',
    position: 'Point Guard',
    birthDate: null,
    gender: 'male' as const,
    bio: 'Professional basketball player',
    profilePicture: {
      url: null,
      key: null,
      uploadedAt: null
    },
    businessInterests: ['fitness', 'lifestyle'],
    socialMedia: {
      instagram: '@johndoe',
      twitter: '@johndoe',
      tiktok: '@johndoe'
    },
    minPayment: {
      shoot: 500,
      inPerson: 750,
      contentShare: 250,
      contentCreation: 400,
      giftedCollab: 100,
      other: 300
    },
    referralSource: 'friend',
    referralName: 'Jane Smith',
    referralVenmo: '@janesmith',
    hometown: 'Chicago, IL',
    userId: new Types.ObjectId('507f1f77bcf86cd799439012')
  }
};

async function testPdfGeneration() {
  console.log('Testing PDFMake PDF generation...');
  
  try {
    const pdfBuffer = await pdfMakeGenerator.generateContractPdf(mockContractData);
    
    // Save the PDF to a test file
    const outputPath = path.join(__dirname, '../../test-output');
    if (!fs.existsSync(outputPath)) {
      fs.mkdirSync(outputPath, { recursive: true });
    }
    
    const fileName = `test-contract-${Date.now()}.pdf`;
    const filePath = path.join(outputPath, fileName);
    
    fs.writeFileSync(filePath, pdfBuffer);
    
    console.log(`✅ PDF generated successfully!`);
    console.log(`📄 File saved to: ${filePath}`);
    console.log(`📊 File size: ${pdfBuffer.length} bytes`);
    
  } catch (error) {
    console.error('❌ PDF generation failed:', error);
    process.exit(1);
  }
}

// Run the test
testPdfGeneration();
